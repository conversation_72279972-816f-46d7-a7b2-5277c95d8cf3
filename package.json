{"name": "jivunie-sacco", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "npm run db:migrate", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate:pg", "db:migrate": "tsx src/db/migrate.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@auth/drizzle-adapter": "^0.7.0", "@hookform/resolvers": "^3.9.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/node": "20.6.2", "@types/nodemailer": "^6.4.17", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "drizzle-kit": "^0.20.7", "drizzle-orm": "^0.29.3", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.446.0", "next": "13.5.1", "next-auth": "^4.24.5", "next-themes": "^0.3.0", "nodemailer": "^6.10.1", "postcss": "8.4.30", "postgres": "^3.4.3", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "swagger-ui-react": "^5.26.2", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "tsx": "^4.7.0", "typescript": "5.2.2", "vaul": "^0.9.9", "winston": "^3.17.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/swagger-ui-react": "^5.18.0"}}